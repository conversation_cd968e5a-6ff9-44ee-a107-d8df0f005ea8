import openpyxl
from pathlib import Path
import yfinance as yf
import pandas as pd

def update_etf_prices(
    excel_file_path: str,
    sheet_name: str = "المحفظة",
    symbol_column: str = "E",
    price_column: str = "I",
    start_row: int = 6,
) -> None:
    """
    Updates ETF prices in an Excel file from Yahoo Finance.

    Args:
        excel_file_path (str): Path to the Excel file.
        sheet_name (str): Name of the sheet containing ETF data.
        symbol_column (str): Column containing ETF symbols (e.g., "E").
        price_column (str): Column to be updated with current prices (e.g., "I").
        start_row (int): Row where the ETF data starts (e.g., 6).
    """
    file_path = Path(excel_file_path)

    # تحقق من وجود الملف
    if not file_path.exists():
        print(f"❌ الملف غير موجود: {file_path}")
        return

    try:
        workbook = openpyxl.load_workbook(file_path)
        if sheet_name not in workbook.sheetnames:
            print(f"❌ الورقة '{sheet_name}' غير موجودة. الأوراق المتاحة: {workbook.sheetnames}")
            return
        sheet = workbook[sheet_name]
    except Exception as e:
        print(f"❌ خطأ في فتح ملف إكسل: {e}")
        return

    # استخراج الرموز من العمود
    symbols = []
    for row_num in range(start_row, sheet.max_row + 1):
        symbol = sheet[f"{symbol_column}{row_num}"].value
        if symbol and isinstance(symbol, str) and not symbol.startswith("="):
            symbols.append(symbol.strip())
        else:
            break

    if not symbols:
        print("⚠️ لم يتم العثور على رموز في العمود المحدد.")
        return

    print(f"✅ الرموز الموجودة: {symbols}")

    try:
        # تحميل الأسعار دفعة واحدة
        data = yf.download(symbols, period="1d")["Close"].iloc[-1]
        if isinstance(data, pd.Series):  # أكثر من رمز
            prices = data.to_dict()
        else:  # رمز واحد فقط
            prices = {symbols[0]: float(data)}
    except Exception as e:
        print(f"❌ خطأ في جلب الأسعار من Yahoo Finance: {e}")
        return

    # تحديث الأسعار في الجدول
    for row_num, symbol in enumerate(symbols, start=start_row):
        price = prices.get(symbol)
        if price:
            sheet[f"{price_column}{row_num}"].value = round(price, 2)
            print(f"🔹 {symbol}: {price:.2f}")
        else:
            print(f"⚠️ لم يتم العثور على سعر للرمز: {symbol}")

    # حفظ الملف باسم جديد
    try:
        new_file_path = file_path.with_name(file_path.stem + "_updated.xlsx")
        workbook.save(new_file_path)
        print(f"✅ تم حفظ الملف بنجاح: {new_file_path}")
    except Exception as e:
        print(f"❌ خطأ أثناء حفظ الملف: {e}")


if __name__ == "__main__":
    excel_file = r"C:\Users\<USER>\Desktop\ETFS\محفظة_استثمارية_احترافية.xlsx"
    update_etf_prices(excel_file)
